# -*- coding: utf-8 -*-
import numpy as np
from scipy.stats import multivariate_normal
import matplotlib.pyplot as plt

def solve_5_1_1():
    """
    5.1.1: Find mean vector and covariance matrix for joint distribution p(y(p), z(r), x(pr))
    
    Given:
    - y(p) ~ N(μp, σp²)
    - z(r) ~ N(νr, τr²)  
    - x(pr) | y(p), z(r) ~ N(y(p) + z(r), σ²)
    - y(p) and z(r) are independent
    
    Following the hint: x(pr) = y(p) + z(r) + ε(pr) where ε(pr) ~ N(0, σ²)
    """
    
    print("5.1.1 Solution:")
    print("================")
    print("The joint distribution is for the vector [y(p), z(r), x(pr)]ᵀ")
    print()
    print("Mean vector μ = [μp, νr, μp + νr]ᵀ")
    print()
    print("Covariance matrix Σ:")
    print("⎡ σp²           0          σp²        ⎤")
    print("⎢ 0            τr²         τr²        ⎥")
    print("⎣ σp²          τr²    σp² + τr² + σ²  ⎦")
    print()
    print("Explanation:")
    print("- Cov(y(p), y(p)) = σp²")
    print("- Cov(z(r), z(r)) = τr²") 
    print("- Cov(x(pr), x(pr)) = Var(y(p) + z(r) + ε(pr)) = σp² + τr² + σ²")
    print("- Cov(y(p), z(r)) = 0 (independence)")
    print("- Cov(y(p), x(pr)) = Cov(y(p), y(p) + z(r) + ε(pr)) = σp²")
    print("- Cov(z(r), x(pr)) = Cov(z(r), y(p) + z(r) + ε(pr)) = τr²")
    
    return {
        'mean': np.array(['μp', 'νr', 'μp + νr']),
        'covariance': np.array([
            ['σp²', '0', 'σp²'],
            ['0', 'τr²', 'τr²'],
            ['σp²', 'τr²', 'σp² + τr² + σ²']
        ])
    }

def solve_5_1_2():
    """
    5.1.2: Derive expression for Q_pr(θ'|θ) = E[log p(y(p), z(r), x(pr)) | x(pr), θ]
    
    This requires finding the conditional distribution p(y(p), z(r) | x(pr))
    """
    
    print("\n5.1.2 Solution:")
    print("================")
    print("For jointly Gaussian variables, the conditional distribution is also Gaussian.")
    print()
    print("Given x(pr), we need p(y(p), z(r) | x(pr))")
    print()
    print("Using the partitioned Gaussian conditioning formula:")
    print("If [y(p), z(r), x(pr)]ᵀ ~ N(μ, Σ), then")
    print("(y(p), z(r) | x(pr)) ~ N(μ_cond, Σ_cond)")
    print()
    print("Where:")
    print("μ_cond = μ₁₂ + Σ₁₂ Σ₂₂⁻¹ (x(pr) - μ₂)")
    print("Σ_cond = Σ₁₁ - Σ₁₂ Σ₂₂⁻¹ Σ₂₁")
    print()
    print("Here:")
    print("- μ₁₂ = [μp, νr]ᵀ")
    print("- μ₂ = μp + νr") 
    print("- Σ₁₂ = [σp², τr²]ᵀ")
    print("- Σ₂₂ = σp² + τr² + σ²")
    print("- Σ₁₁ = [[σp², 0], [0, τr²]]")
    print()
    print("Therefore:")
    print("μ_cond = [μp, νr]ᵀ + [σp², τr²]ᵀ / (σp² + τr² + σ²) × (x(pr) - μp - νr)")
    print()
    print("Let α = σp²/(σp² + τr² + σ²) and β = τr²/(σp² + τr² + σ²)")
    print()
    print("μ_cond = [μp + α(x(pr) - μp - νr), νr + β(x(pr) - μp - νr)]ᵀ")
    print()
    print("Σ_cond = [[σp² - α·σp², -α·τr²], [-β·σp², τr² - β·τr²]]")
    print("       = [[σp²σ²/(σp² + τr² + σ²), -σp²τr²/(σp² + τr² + σ²)],")
    print("          [-σp²τr²/(σp² + τr² + σ²), τr²σ²/(σp² + τr² + σ²)]]")
    print()
    print("The Q function becomes:")
    print("Q_pr(θ'|θ) = E[log p(y(p), z(r), x(pr)) | x(pr), θ]")
    print("           = constant - ½E[(y(p) - μp)²/σp² + (z(r) - νr)²/τr² + (x(pr) - y(p) - z(r))²/σ²]")
    
    return {
        'conditional_mean': 'μp + α(x(pr) - μp - νr), νr + β(x(pr) - μp - νr)',
        'conditional_cov': 'As derived above',
        'alpha': 'σp²/(σp² + τr² + σ²)',
        'beta': 'τr²/(σp² + τr² + σ²)'
    }

def solve_5_2():
    """
    5.2: Derive M-step updates for parameters μp, σp², νr, τr²
    """
    
    print("\n5.2 Solution:")
    print("==============")
    print("M-step: Maximize Q(θ'|θ) with respect to θ' = {μp, σp², νr, τr²}")
    print()
    print("From the E-step, we have:")
    print("E[y(p) | x(pr)] = μp + α(x(pr) - μp - νr)")
    print("E[z(r) | x(pr)] = νr + β(x(pr) - μp - νr)")
    print("E[y(p)²| x(pr)] = Var[y(p)|x(pr)] + (E[y(p)|x(pr)])²")
    print("E[z(r)²| x(pr)] = Var[z(r)|x(pr)] + (E[z(r)|x(pr)])²")
    print()
    print("Taking derivatives of Q(θ'|θ) and setting to zero:")
    print()
    print("1. Update for μp:")
    print("   ∂Q/∂μp = 0 gives:")
    print("   μp_new = (1/R) Σr E[y(p) | x(pr)]")
    print("          = (1/R) Σr [μp + α(x(pr) - μp - νr)]")
    print()
    print("2. Update for νr:")
    print("   ∂Q/∂νr = 0 gives:")
    print("   νr_new = (1/P) Σp E[z(r) | x(pr)]")
    print("          = (1/P) Σp [νr + β(x(pr) - μp - νr)]")
    print()
    print("3. Update for σp²:")
    print("   ∂Q/∂σp² = 0 gives:")
    print("   σp²_new = (1/R) Σr E[(y(p) - μp_new)² | x(pr)]")
    print("           = (1/R) Σr [Var[y(p)|x(pr)] + (E[y(p)|x(pr)] - μp_new)²]")
    print()
    print("4. Update for τr²:")
    print("   ∂Q/∂τr² = 0 gives:")
    print("   τr²_new = (1/P) Σp E[(z(r) - νr_new)² | x(pr)]")
    print("           = (1/P) Σp [Var[z(r)|x(pr)] + (E[z(r)|x(pr)] - νr_new)²]")
    
    return {
        'mu_p_update': '(1/R) Σr E[y(p) | x(pr)]',
        'nu_r_update': '(1/P) Σp E[z(r) | x(pr)]',
        'sigma_p_sq_update': '(1/R) Σr E[(y(p) - μp_new)² | x(pr)]',
        'tau_r_sq_update': '(1/P) Σp E[(z(r) - νr_new)² | x(pr)]'
    }

def em_algorithm_implementation(X, max_iter=100, tol=1e-6):
    """
    Complete EM algorithm implementation for the review de-biasing problem
    
    Args:
        X: P x R matrix of review scores where X[p,r] = x(pr)
        max_iter: Maximum number of iterations
        tol: Convergence tolerance
    
    Returns:
        Dictionary containing estimated parameters
    """
    P, R = X.shape
    
    # Initialize parameters
    mu_p = np.mean(X, axis=1)  # Average score per paper
    nu_r = np.mean(X, axis=0)  # Average score per reviewer
    sigma_p_sq = np.var(X, axis=1)  # Variance per paper
    tau_r_sq = np.var(X, axis=0)    # Variance per reviewer
    sigma_sq = 1.0  # Fixed noise variance
    
    log_likelihood = []
    
    for iteration in range(max_iter):
        # E-step: Compute expectations
        E_y = np.zeros((P, R))
        E_z = np.zeros((P, R))
        Var_y = np.zeros((P, R))
        Var_z = np.zeros((P, R))
        
        for p in range(P):
            for r in range(R):
                # Compute α and β
                denom = sigma_p_sq[p] + tau_r_sq[r] + sigma_sq
                alpha = sigma_p_sq[p] / denom
                beta = tau_r_sq[r] / denom
                
                # Conditional expectations
                residual = X[p, r] - mu_p[p] - nu_r[r]
                E_y[p, r] = mu_p[p] + alpha * residual
                E_z[p, r] = nu_r[r] + beta * residual
                
                # Conditional variances
                Var_y[p, r] = sigma_p_sq[p] * sigma_sq / denom
                Var_z[p, r] = tau_r_sq[r] * sigma_sq / denom
        
        # M-step: Update parameters
        mu_p_new = np.mean(E_y, axis=1)
        nu_r_new = np.mean(E_z, axis=0)
        
        sigma_p_sq_new = np.mean(Var_y + (E_y - mu_p_new[:, np.newaxis])**2, axis=1)
        tau_r_sq_new = np.mean(Var_z + (E_z - nu_r_new[np.newaxis, :])**2, axis=0)
        
        # Check convergence
        change = (np.sum((mu_p_new - mu_p)**2) + np.sum((nu_r_new - nu_r)**2) + 
                 np.sum((sigma_p_sq_new - sigma_p_sq)**2) + np.sum((tau_r_sq_new - tau_r_sq)**2))
        
        if change < tol:
            print(f"Converged after {iteration + 1} iterations")
            break
            
        # Update parameters
        mu_p = mu_p_new
        nu_r = nu_r_new
        sigma_p_sq = sigma_p_sq_new
        tau_r_sq = tau_r_sq_new
        
        # Compute log-likelihood (optional, for monitoring)
        ll = 0
        for p in range(P):
            for r in range(R):
                mean_x = mu_p[p] + nu_r[r]
                var_x = sigma_p_sq[p] + tau_r_sq[r] + sigma_sq
                ll += -0.5 * np.log(2 * np.pi * var_x) - 0.5 * (X[p, r] - mean_x)**2 / var_x
        log_likelihood.append(ll)
    
    return {
        'mu_p': mu_p,
        'nu_r': nu_r, 
        'sigma_p_sq': sigma_p_sq,
        'tau_r_sq': tau_r_sq,
        'log_likelihood': log_likelihood,
        'iterations': iteration + 1
    }

def example_usage():
    """
    Example usage of the EM algorithm with synthetic data
    """
    np.random.seed(42)
    
    # Generate synthetic data
    P, R = 10, 5  # 10 papers, 5 reviewers
    true_mu_p = np.random.normal(0, 2, P)  # True paper quality
    true_nu_r = np.random.normal(0, 1, R)  # True reviewer bias
    sigma_sq = 0.5  # Noise variance
    
    # Generate review scores
    X = np.zeros((P, R))
    for p in range(P):
        for r in range(R):
            y_p = np.random.normal(true_mu_p[p], 1.0)
            z_r = np.random.normal(true_nu_r[r], 0.8)
            X[p, r] = y_p + z_r + np.random.normal(0, np.sqrt(sigma_sq))
    
    print("Example Usage:")
    print("==============")
    print(f"Generated synthetic data: {P} papers, {R} reviewers")
    print(f"Review scores matrix shape: {X.shape}")
    print(f"Sample scores:\n{X[:3, :3]}")
    
    # Run EM algorithm
    results = em_algorithm_implementation(X)
    
    print(f"\nEstimated parameters after {results['iterations']} iterations:")
    print(f"Paper qualities (μp): {results['mu_p'][:5]}")
    print(f"True paper qualities: {true_mu_p[:5]}")
    print(f"Reviewer biases (νr): {results['nu_r']}")
    print(f"True reviewer biases: {true_nu_r}")
    
    return results

# Run the solutions
if __name__ == "__main__":
    solve_5_1_1()
    solve_5_1_2() 
    solve_5_2()
    print("\n" + "="*50)
    example_usage()