import numpy as np

# Given bathroom counts
x2 = np.array([1, 2, 2, 3, 1, 2, 3, 2, 1, 2])
m = len(x2)

# Compute x1 using the correlation x1 = 1.5 * x2
x1 = 1.5 * x2

# Construct design matrix X = [1; x1; x2] (3 x m)
X = np.vstack([
    np.ones(m),  # intercept row
    x1,          # bedrooms row
    x2           # bathrooms row
])

print("Design matrix X:")
print(X)
print(f"\nShape of X: {X.shape}")

# Compute sample covariance matrix G = (1/m) * X * X^T
G = (1/m) * np.dot(X, X.T)

print("\nSample covariance matrix G:")
print(G)

# Compute T and S for analytical verification
T = np.sum(x2)
S = np.sum(x2**2)

print(f"\nT = sum(x2) = {T}")
print(f"S = sum(x2^2) = {S}")

# Analytical G matrix
G_analytical = np.array([
    [1, 1.5*T/m, T/m],
    [1.5*T/m, 2.25*S/m, 1.5*S/m],
    [T/m, 1.5*S/m, S/m]
])

print("\nAnalytical G matrix:")
print(G_analytical)

print(f"\nDifference between numerical and analytical G:")
print(np.abs(G - G_analytical))

# Check rank of G
rank_G = np.linalg.matrix_rank(G)
print(f"\nRank of G: {rank_G}")

# Check linear dependence: Row 2 should equal 1.5 * Row 3
print(f"\nRow 2 of G: {G[1, :]}")
print(f"1.5 * Row 3 of G: {1.5 * G[2, :]}")
print(f"Difference: {G[1, :] - 1.5 * G[2, :]}")

# Find eigenvalues and eigenvectors
eigenvalues, eigenvectors = np.linalg.eig(G)

print(f"\nEigenvalues: {eigenvalues}")
print(f"Eigenvectors (columns):\n{eigenvectors}")

# Sort eigenvalues and eigenvectors by eigenvalue magnitude
idx = np.argsort(eigenvalues)[::-1]  # descending order
eigenvalues_sorted = eigenvalues[idx]
eigenvectors_sorted = eigenvectors[:, idx]

print(f"\nSorted eigenvalues: {eigenvalues_sorted}")
print(f"Largest eigenvector: {eigenvectors_sorted[:, 0]}")

# Verify the null space eigenvector [0, 1, -1.5]
null_eigenvector = np.array([0, 1, -1.5])
result = np.dot(G, null_eigenvector)
print(f"\nNull space verification G * [0, 1, -1.5]^T = {result}")
print(f"Is this approximately zero? {np.allclose(result, 0)}")