import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.neighbors import KernelDensity
from scipy.stats import gaussian_kde
import warnings
warnings.filterwarnings('ignore')

# Set up plotting parameters
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

# number of bins
BINS = 10

# Load the data
data = pd.read_csv('data/n90pol.csv')
print("data/n90pol.csv loaded successfully!")
print(f"Data shape: {data.shape}")
print("\nFirst few rows:")
print(data.head())
print("\nData summary:")
print(data.describe())
print("\nOrientation value counts:")
print(data['orientation'].value_counts().sort_index())

# Extract variables
amygdala = data['amygdala'].values
acc = data['acc'].values
orientation = data['orientation'].values

print("="*60)
print("QUESTION 2.1: 1D Histogram and KDE for amygdala and acc")
print("="*60)

# 2.1: 1-dimensional histogram and KDE for amygdala and acc
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Amygdala - Histogram
axes[0,0].hist(amygdala, bins=BINS, density=True, alpha=0.7, color='skyblue', edgecolor='black')
axes[0,0].set_title(f'Histogram: Amygdala Volume (Bins={BINS})')
axes[0,0].set_xlabel('Amygdala (residual volume)')
axes[0,0].set_ylabel('Density')
axes[0,0].grid(True, alpha=0.3)

# Amygdala - KDE
x_range = np.linspace(amygdala.min() - 0.02, amygdala.max() + 0.02, 200)
kde_amygdala = gaussian_kde(amygdala)
kde_amygdala.set_bandwidth(kde_amygdala.factor * 0.8)  # Adjust bandwidth
axes[0,1].plot(x_range, kde_amygdala(x_range), 'r-', linewidth=2)
axes[0,1].fill_between(x_range, kde_amygdala(x_range), alpha=0.3, color='red')
axes[0,1].set_title('KDE: Amygdala Volume')
axes[0,1].set_xlabel('Amygdala (residual volume)')
axes[0,1].set_ylabel('Density')
axes[0,1].grid(True, alpha=0.3)

# ACC - Histogram
axes[1,0].hist(acc, bins=BINS, density=True, alpha=0.7, color='lightgreen', edgecolor='black')
axes[1,0].set_title(f'Histogram: ACC Volume (Bins={BINS})')
axes[1,0].set_xlabel('ACC (residual volume)')
axes[1,0].set_ylabel('Density')
axes[1,0].grid(True, alpha=0.3)

# ACC - KDE
x_range_acc = np.linspace(acc.min() - 0.01, acc.max() + 0.01, 200)
kde_acc = gaussian_kde(acc)
kde_acc.set_bandwidth(kde_acc.factor * 0.8)  # Adjust bandwidth
axes[1,1].plot(x_range_acc, kde_acc(x_range_acc), 'g-', linewidth=2)
axes[1,1].fill_between(x_range_acc, kde_acc(x_range_acc), alpha=0.3, color='green')
axes[1,1].set_title('KDE: ACC Volume')
axes[1,1].set_xlabel('ACC (residual volume)')
axes[1,1].set_ylabel('Density')
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("="*60)
print("QUESTION 2.2: 2D Histogram for (amygdala, acc)")
print("="*60)

# 2.2: 2-dimensional histogram
plt.figure(figsize=(10, 8))
plt.hist2d(amygdala, acc, bins=BINS, density=True, cmap='Blues')
plt.colorbar(label='Density')
plt.xlabel('Amygdala (residual volume)')
plt.ylabel('ACC (residual volume)')
plt.title(f'2D Histogram: Amygdala vs ACC Volume (Bins={BINS})')
plt.grid(True, alpha=0.3)
plt.show()

print("="*60)
print("QUESTION 2.3: 2D KDE for (amygdala, acc)")
print("="*60)

# 2.3: 2-dimensional KDE
# Create a grid for evaluation
x_min, x_max = amygdala.min() - 0.02, amygdala.max() + 0.02
y_min, y_max = acc.min() - 0.01, acc.max() + 0.01
xx, yy = np.meshgrid(np.linspace(x_min, x_max, 100),
                     np.linspace(y_min, y_max, 100))

# Perform 2D KDE
positions = np.vstack([amygdala, acc])
kernel_2d = gaussian_kde(positions)
kernel_2d.set_bandwidth(kernel_2d.factor * 0.6)  # Adjust bandwidth
f = np.reshape(kernel_2d(np.vstack([xx.ravel(), yy.ravel()])).T, xx.shape)

# Plot 2D KDE as heatmap
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Heatmap
im = ax1.imshow(np.rot90(f), cmap='viridis', extent=[x_min, x_max, y_min, y_max], aspect='auto')
ax1.scatter(amygdala, acc, s=20, c='white', alpha=0.6)
ax1.set_xlabel('Amygdala (residual volume)')
ax1.set_ylabel('ACC (residual volume)')
ax1.set_title('2D KDE Heatmap: Amygdala vs ACC')
plt.colorbar(im, ax=ax1, label='Density')

# Contour plot
contour = ax2.contour(xx, yy, f, levels=8, colors='black', linewidths=1, alpha=0.6)
ax2.contourf(xx, yy, f, levels=20, cmap='viridis', alpha=0.8)
ax2.scatter(amygdala, acc, s=20, c='white', alpha=0.7)
ax2.set_xlabel('Amygdala (residual volume)')
ax2.set_ylabel('ACC (residual volume)')
ax2.set_title('2D KDE Contour Plot: Amygdala vs ACC')
plt.colorbar(contour, ax=ax2, label='Density')

plt.tight_layout()
plt.show()

# Analysis of 2D distribution
print("\nAnalysis of 2D KDE:")
print("1. Modality: The distribution appears to be unimodal with a single main peak.")
print("2. Outliers: There appear to be some data points in the tails of the distribution.")

# Calculate correlation to assess independence
correlation = np.corrcoef(amygdala, acc)[0, 1]
print(f"3. Independence: Correlation coefficient = {correlation:.4f}")
if abs(correlation) < 0.1:
    print("   The variables appear to be nearly independent (weak correlation).")
elif abs(correlation) < 0.3:
    print("   The variables show weak dependence.")
else:
    print("   The variables show moderate to strong dependence.")

print("="*60)
print("QUESTION 2.4: Conditional distributions by orientation")
print("="*60)

# 2.4: Conditional distributions
orientations = sorted(data['orientation'].unique())
colors = ['blue', 'green', 'orange', 'red']

# Plot conditional distributions for amygdala
plt.figure(figsize=(15, 6))

plt.subplot(1, 2, 1)
x_range = np.linspace(amygdala.min() - 0.02, amygdala.max() + 0.02, 200)
conditional_means_amygdala = {}

for i, orient in enumerate(orientations):
    subset = amygdala[orientation == orient]
    if len(subset) > 1:
        kde = gaussian_kde(subset)
        kde.set_bandwidth(kde.factor * 0.8)
        plt.plot(x_range, kde(x_range), label=f'Orientation {orient}', 
                color=colors[i], linewidth=2)
        conditional_means_amygdala[orient] = np.mean(subset)

plt.xlabel('Amygdala (residual volume)')
plt.ylabel('Density')
plt.title('Conditional KDE: Amygdala | Orientation')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot conditional distributions for acc
plt.subplot(1, 2, 2)
x_range_acc = np.linspace(acc.min() - 0.01, acc.max() + 0.01, 200)
conditional_means_acc = {}

for i, orient in enumerate(orientations):
    subset = acc[orientation == orient]
    if len(subset) > 1:
        kde = gaussian_kde(subset)
        kde.set_bandwidth(kde.factor * 0.8)
        plt.plot(x_range_acc, kde(x_range_acc), label=f'Orientation {orient}', 
                color=colors[i], linewidth=2)
        conditional_means_acc[orient] = np.mean(subset)

plt.xlabel('ACC (residual volume)')
plt.ylabel('Density')
plt.title('Conditional KDE: ACC | Orientation')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print conditional means table
print("\nConditional Sample Means:")
print("+" + "-"*50 + "+")
print(f"| {'':15} | {' c = 2':8} | {' c = 3':8} | {' c = 4':8} | {' c = 5':8} |")
print("+" + "-"*50 + "+")

amygdala_row = f"| {'amygdala':15} |"
acc_row = f"| {'acc':15} |"

for orient in [2, 3, 4, 5]:
    if orient in conditional_means_amygdala:
        amygdala_row += f" {conditional_means_amygdala[orient]:7.4f} |"
    else:
        amygdala_row += f" {'N/A':7} |"
    
    if orient in conditional_means_acc:
        acc_row += f" {conditional_means_acc[orient]:7.4f} |"
    else:
        acc_row += f" {'N/A':7} |"

print(amygdala_row)
print(acc_row)
print("+" + "-"*50 + "+")

# Analysis
print("\nAnalysis of conditional distributions:")
print("Differences in amygdala distributions across orientations:")
amyg_means = list(conditional_means_amygdala.values())
if len(amyg_means) > 1:
    print(f"- Range of means: {min(amyg_means):.4f} to {max(amyg_means):.4f}")
    print(f"- Standard deviation of means: {np.std(amyg_means):.4f}")

print("Differences in ACC distributions across orientations:")
acc_means = list(conditional_means_acc.values())
if len(acc_means) > 1:
    print(f"- Range of means: {min(acc_means):.4f} to {max(acc_means):.4f}")
    print(f"- Standard deviation of means: {np.std(acc_means):.4f}")

print("="*60)
print("QUESTION 2.5: Conditional joint distributions")
print("="*60)

# 2.5: Conditional joint distributions
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for i, orient in enumerate(orientations):
    if i < 4:  # Ensure we don't exceed subplot count
        subset_data = data[data['orientation'] == orient]
        if len(subset_data) > 5:  # Need sufficient data points
            amyg_subset = subset_data['amygdala'].values
            acc_subset = subset_data['acc'].values
            
            # Create grid
            x_min_sub = amyg_subset.min() - 0.01
            x_max_sub = amyg_subset.max() + 0.01
            y_min_sub = acc_subset.min() - 0.01
            y_max_sub = acc_subset.max() + 0.01
            
            xx_sub, yy_sub = np.meshgrid(np.linspace(x_min_sub, x_max_sub, 50),
                                        np.linspace(y_min_sub, y_max_sub, 50))
            
            # 2D KDE for subset
            if len(amyg_subset) > 1:
                positions_sub = np.vstack([amyg_subset, acc_subset])
                kernel_2d_sub = gaussian_kde(positions_sub)
                kernel_2d_sub.set_bandwidth(kernel_2d_sub.factor * 0.8)
                f_sub = np.reshape(kernel_2d_sub(np.vstack([xx_sub.ravel(), yy_sub.ravel()])).T, 
                                 xx_sub.shape)
                
                # Plot
                im = axes[i].contourf(xx_sub, yy_sub, f_sub, levels=10, cmap='viridis', alpha=0.8)
                axes[i].scatter(amyg_subset, acc_subset, s=30, c='white', alpha=0.8)
                axes[i].set_xlabel('Amygdala')
                axes[i].set_ylabel('ACC')
                axes[i].set_title(f'Conditional Joint KDE | Orientation = {orient}')
                plt.colorbar(im, ax=axes[i], label='Density')
        else:
            axes[i].text(0.5, 0.5, f'Insufficient data\nfor Orientation {orient}', 
                        ha='center', va='center', transform=axes[i].transAxes)
            axes[i].set_title(f'Orientation = {orient}')

plt.tight_layout()
plt.show()

print("\nAnalysis of conditional joint distributions:")
print("The conditional joint distributions show different patterns across political orientations.")
print("This suggests that brain structure (amygdala and ACC volumes) may be related to political views.")
print("The differences in shape and location of the density contours indicate that the")
print("joint distribution of brain volumes varies with political orientation.")

# Statistical summary
print("\n" + "="*60)
print("SUMMARY STATISTICS BY ORIENTATION")
print("="*60)
for orient in orientations:
    subset = data[data['orientation'] == orient]
    if len(subset) > 0:
        print(f"\nOrientation {orient} (n={len(subset)}):")
        print(f"  Amygdala: mean={subset['amygdala'].mean():.4f}, std={subset['amygdala'].std():.4f}")
        print(f"  ACC: mean={subset['acc'].mean():.4f}, std={subset['acc'].std():.4f}")
        print(f"  Correlation: {subset['amygdala'].corr(subset['acc']):.4f}")