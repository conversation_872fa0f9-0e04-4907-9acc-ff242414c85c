import numpy as np
import matplotlib.pyplot as plt
from scipy.io import loadmat
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import seaborn as sns

class GaussianMixtureModel:
    def __init__(self, n_components=2, max_iter=100, tol=1e-6):
        self.n_components = n_components
        self.max_iter = max_iter
        self.tol = tol
        
    def _initialize_parameters(self, X):
        """Initialize GMM parameters"""
        n_samples, n_features = X.shape
        
        # Initialize weights uniformly
        self.weights = np.ones(self.n_components) / self.n_components
        
        # Initialize means: random Gaussian vectors with zero mean
        self.means = np.random.randn(self.n_components, n_features)
        
        # Initialize covariances as specified in the problem
        self.covariances = []
        for k in range(self.n_components):
            S = np.random.randn(n_features, n_features)
            cov = S @ S.T + np.eye(n_features)
            self.covariances.append(cov)
        self.covariances = np.array(self.covariances)
        
    def _gaussian_pdf(self, X, mean, cov):
        """Compute multivariate Gaussian PDF"""
        n_features = X.shape[1]
        diff = X - mean
        inv_cov = np.linalg.inv(cov)
        det_cov = np.linalg.det(cov)
        
        # Compute the exponent
        exponent = -0.5 * np.sum(diff @ inv_cov * diff, axis=1)
        
        # Compute the normalization constant
        normalization = 1.0 / np.sqrt((2 * np.pi) ** n_features * det_cov)
        
        return normalization * np.exp(exponent)
    
    def _e_step(self, X):
        """E-step: compute responsibilities"""
        n_samples = X.shape[0]
        responsibilities = np.zeros((n_samples, self.n_components))
        
        # Compute likelihood for each component
        for k in range(self.n_components):
            responsibilities[:, k] = self.weights[k] * self._gaussian_pdf(
                X, self.means[k], self.covariances[k]
            )
        
        # Normalize to get responsibilities
        row_sums = responsibilities.sum(axis=1, keepdims=True)
        responsibilities = responsibilities / (row_sums + 1e-10)
        
        return responsibilities
    
    def _m_step(self, X, responsibilities):
        """M-step: update parameters"""
        n_samples, n_features = X.shape
        
        # Update weights
        Nk = responsibilities.sum(axis=0)
        self.weights = Nk / n_samples
        
        # Update means
        for k in range(self.n_components):
            self.means[k] = (responsibilities[:, k].reshape(-1, 1) * X).sum(axis=0) / Nk[k]
        
        # Update covariances
        for k in range(self.n_components):
            diff = X - self.means[k]
            weighted_diff = responsibilities[:, k].reshape(-1, 1) * diff
            self.covariances[k] = (weighted_diff.T @ diff) / Nk[k]
            
            # Add small regularization to avoid singular matrices
            self.covariances[k] += 1e-6 * np.eye(n_features)
    
    def _compute_log_likelihood(self, X):
        """Compute log-likelihood of the data"""
        log_likelihood = 0
        for i in range(X.shape[0]):
            sample_likelihood = 0
            for k in range(self.n_components):
                sample_likelihood += self.weights[k] * self._gaussian_pdf(
                    X[i:i+1], self.means[k], self.covariances[k]
                )[0]
            log_likelihood += np.log(sample_likelihood + 1e-10)
        return log_likelihood
    
    def fit(self, X):
        """Fit GMM using EM algorithm"""
        self._initialize_parameters(X)
        log_likelihoods = []
        
        for iteration in range(self.max_iter):
            # E-step
            responsibilities = self._e_step(X)
            
            # M-step
            self._m_step(X, responsibilities)
            
            # Compute log-likelihood
            log_likelihood = self._compute_log_likelihood(X)
            log_likelihoods.append(log_likelihood)
            
            # Check for convergence
            if iteration > 0 and abs(log_likelihoods[-1] - log_likelihoods[-2]) < self.tol:
                print(f"Converged after {iteration + 1} iterations")
                break
        
        self.log_likelihoods = log_likelihoods
        return responsibilities
    
    def predict(self, X):
        """Predict cluster assignments"""
        responsibilities = self._e_step(X)
        return np.argmax(responsibilities, axis=1), responsibilities

def load_data():
    """Load MNIST data for digits 2 and 6"""
    try:
        # Try to load .mat files first
        data = loadmat('data/data.mat')
        labels = loadmat('data/label.mat')
        images = data['images']
        true_labels = labels['labels'].flatten()
    except:
        try:
            # Try to load .dat files
            images = np.loadtxt('data/data.dat')
            true_labels = np.loadtxt('data/label.dat', dtype=int)
        except:
            print("Could not load data files. Please ensure data files are in the correct location.")
            return None, None
    
    return images.T, true_labels  # Transpose to get samples as rows

def apply_pca(X, n_components=4):
    """Apply PCA to reduce dimensionality"""
    pca = PCA(n_components=n_components)
    X_reduced = pca.fit_transform(X)
    return X_reduced, pca

def visualize_results(gmm, pca, true_labels, predicted_labels, responsibilities):
    """Visualize all results"""
    fig = plt.figure(figsize=(20, 15))
    
    # 1. Plot log-likelihood convergence
    plt.subplot(3, 4, 1)
    plt.plot(gmm.log_likelihoods)
    plt.title('Log-Likelihood vs Iterations')
    plt.xlabel('Iteration')
    plt.ylabel('Log-Likelihood')
    plt.grid(True)
    
    # 2. Display component means as images
    for k in range(2):
        plt.subplot(3, 4, 2 + k)
        # Transform back to original space
        mean_original = pca.inverse_transform(gmm.means[k].reshape(1, -1))
        mean_image = mean_original.reshape(28, 28)
        plt.imshow(mean_image, cmap='gray')
        plt.title(f'Component {k+1} Mean\n(Weight: {gmm.weights[k]:.3f})')
        plt.axis('off')
    
    # 3. Display covariance matrices as heatmaps
    for k in range(2):
        plt.subplot(3, 4, 4 + k)
        sns.heatmap(gmm.covariances[k], annot=True, fmt='.3f', cmap='viridis')
        plt.title(f'Component {k+1} Covariance')
    
    # 4. Show some sample images with predictions
    sample_indices = np.random.choice(len(predicted_labels), 6, replace=False)
    for i, idx in enumerate(sample_indices):
        plt.subplot(3, 4, 7 + i)
        # Get original image
        original_image = pca.inverse_transform(X_pca[idx].reshape(1, -1))
        image = original_image.reshape(28, 28)
        plt.imshow(image, cmap='gray')
        plt.title(f'True: {true_labels[idx]}, Pred: {predicted_labels[idx]}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def calculate_misclassification_rate(true_labels, predicted_labels):
    """Calculate misclassification rates for each digit"""
    # Map predicted labels to true labels (handle label switching)
    # Find the best mapping between predicted and true labels
    unique_true = np.unique(true_labels)
    unique_pred = np.unique(predicted_labels)
    
    # Try both possible mappings
    mapping1 = {unique_pred[0]: unique_true[0], unique_pred[1]: unique_true[1]}
    mapping2 = {unique_pred[0]: unique_true[1], unique_pred[1]: unique_true[0]}
    
    mapped_pred1 = np.array([mapping1[p] for p in predicted_labels])
    mapped_pred2 = np.array([mapping2[p] for p in predicted_labels])
    
    accuracy1 = np.mean(mapped_pred1 == true_labels)
    accuracy2 = np.mean(mapped_pred2 == true_labels)
    
    # Choose the mapping with higher accuracy
    if accuracy1 >= accuracy2:
        mapped_pred = mapped_pred1
        accuracy = accuracy1
    else:
        mapped_pred = mapped_pred2
        accuracy = accuracy2
    
    # Calculate misclassification rates for each digit
    digit_2_mask = true_labels == 2
    digit_6_mask = true_labels == 6
    
    digit_2_misclassification = 1 - np.mean(mapped_pred[digit_2_mask] == true_labels[digit_2_mask])
    digit_6_misclassification = 1 - np.mean(mapped_pred[digit_6_mask] == true_labels[digit_6_mask])
    
    overall_misclassification = 1 - accuracy
    
    return digit_2_misclassification, digit_6_misclassification, overall_misclassification

# Main execution
if __name__ == "__main__":
    print("Loading MNIST data (digits 2 and 6)...")
    images, true_labels = load_data()
    
    if images is None:
        print("Failed to load data. Please check file paths.")
        exit()
    
    print(f"Data shape: {images.shape}")
    print(f"Labels shape: {true_labels.shape}")
    print(f"Unique labels: {np.unique(true_labels)}")
    
    # Apply PCA to reduce dimensionality to 4
    print("\nApplying PCA to reduce dimensionality to 4...")
    X_pca, pca = apply_pca(images, n_components=4)
    print(f"PCA transformed data shape: {X_pca.shape}")
    print(f"Explained variance ratio: {pca.explained_variance_ratio_}")
    print(f"Total explained variance: {np.sum(pca.explained_variance_ratio_):.4f}")
    
    # Fit GMM using EM algorithm
    print("\nFitting Gaussian Mixture Model using EM algorithm...")
    gmm = GaussianMixtureModel(n_components=2, max_iter=100, tol=1e-6)
    responsibilities = gmm.fit(X_pca)
    
    # Get predictions
    predicted_labels, _ = gmm.predict(X_pca)
    
    # Calculate misclassification rates for GMM
    digit_2_miscl_gmm, digit_6_miscl_gmm, overall_miscl_gmm = calculate_misclassification_rate(
        true_labels, predicted_labels
    )
    
    print("\n" + "="*50)
    print("GMM RESULTS:")
    print("="*50)
    print(f"Component weights: {gmm.weights}")
    print(f"\nComponent means (in PCA space):")
    for k in range(2):
        print(f"  Component {k+1}: {gmm.means[k]}")
    
    print(f"\nMisclassification rates (GMM):")
    print(f"  Digit 2: {digit_2_miscl_gmm:.4f} ({digit_2_miscl_gmm*100:.2f}%)")
    print(f"  Digit 6: {digit_6_miscl_gmm:.4f} ({digit_6_miscl_gmm*100:.2f}%)")
    print(f"  Overall: {overall_miscl_gmm:.4f} ({overall_miscl_gmm*100:.2f}%)")
    
    # Compare with K-means
    print("\nComparing with K-means clustering...")
    kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
    kmeans_labels = kmeans.fit_predict(X_pca)
    
    # Calculate misclassification rates for K-means
    digit_2_miscl_kmeans, digit_6_miscl_kmeans, overall_miscl_kmeans = calculate_misclassification_rate(
        true_labels, kmeans_labels
    )
    
    print("\n" + "="*50)
    print("K-MEANS RESULTS:")
    print("="*50)
    print(f"Misclassification rates (K-means):")
    print(f"  Digit 2: {digit_2_miscl_kmeans:.4f} ({digit_2_miscl_kmeans*100:.2f}%)")
    print(f"  Digit 6: {digit_6_miscl_kmeans:.4f} ({digit_6_miscl_kmeans*100:.2f}%)")
    print(f"  Overall: {overall_miscl_kmeans:.4f} ({overall_miscl_kmeans*100:.2f}%)")
    
    print("\n" + "="*50)
    print("COMPARISON:")
    print("="*50)
    if overall_miscl_gmm < overall_miscl_kmeans:
        print("GMM achieves better performance than K-means")
    elif overall_miscl_gmm > overall_miscl_kmeans:
        print("K-means achieves better performance than GMM")
    else:
        print("GMM and K-means achieve similar performance")
    
    print(f"Improvement: {abs(overall_miscl_gmm - overall_miscl_kmeans)*100:.2f} percentage points")
    
    # Visualize results
    print("\nGenerating visualizations...")
    visualize_results(gmm, pca, true_labels, predicted_labels, responsibilities)
    
    # Display covariance matrices separately for better visibility
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    for k in range(2):
        im = axes[k].imshow(gmm.covariances[k], cmap='viridis')
        axes[k].set_title(f'Component {k+1} Covariance Matrix\n(Weight: {gmm.weights[k]:.3f})')
        plt.colorbar(im, ax=axes[k])
        
        # Add text annotations
        for i in range(4):
            for j in range(4):
                text = axes[k].text(j, i, f'{gmm.covariances[k][i, j]:.3f}',
                                   ha="center", va="center", color="white", fontsize=10)
    
    plt.tight_layout()
    plt.show()