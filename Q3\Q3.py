import numpy as np
import matplotlib.pyplot as plt
from scipy.io import loadmat
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans

def initialize_gmm_parameters(X, n_components=2):
    """Initialize GMM parameters according to problem specifications"""
    _, n_features = X.shape

    # Initialize weights uniformly
    weights = np.ones(n_components) / n_components

    # Initialize means: random Gaussian vectors with zero mean
    means = np.random.randn(n_components, n_features)

    # Initialize covariances as specified in the problem
    covariances = []
    for _ in range(n_components):
        S = np.random.randn(n_features, n_features)
        cov = S @ S.T + np.eye(n_features)
        covariances.append(cov)
    covariances = np.array(covariances)

    return weights, means, covariances

def gaussian_pdf(X, mean, cov):
    """Compute multivariate Gaussian PDF"""
    n_features = X.shape[1]
    diff = X - mean
    inv_cov = np.linalg.inv(cov)
    det_cov = np.linalg.det(cov)

    # Compute the exponent
    exponent = -0.5 * np.sum(diff @ inv_cov * diff, axis=1)

    # Compute the normalization constant
    normalization = 1.0 / np.sqrt((2 * np.pi) ** n_features * det_cov)

    return normalization * np.exp(exponent)

def e_step(X, weights, means, covariances):
    """E-step: compute responsibilities (tau_k^i)"""
    n_samples = X.shape[0]
    n_components = len(weights)
    responsibilities = np.zeros((n_samples, n_components))

    # Compute likelihood for each component
    for k in range(n_components):
        responsibilities[:, k] = weights[k] * gaussian_pdf(X, means[k], covariances[k])

    # Normalize to get responsibilities
    row_sums = responsibilities.sum(axis=1, keepdims=True)
    responsibilities = responsibilities / (row_sums + 1e-10)

    return responsibilities

def m_step(X, responsibilities):
    """M-step: update parameters"""
    n_samples, n_features = X.shape
    n_components = responsibilities.shape[1]

    # Update weights
    Nk = responsibilities.sum(axis=0)
    weights = Nk / n_samples

    # Update means
    means = np.zeros((n_components, n_features))
    for k in range(n_components):
        means[k] = (responsibilities[:, k].reshape(-1, 1) * X).sum(axis=0) / Nk[k]

    # Update covariances
    covariances = np.zeros((n_components, n_features, n_features))
    for k in range(n_components):
        diff = X - means[k]
        weighted_diff = responsibilities[:, k].reshape(-1, 1) * diff
        covariances[k] = (weighted_diff.T @ diff) / Nk[k]

        # Add small regularization to avoid singular matrices
        covariances[k] += 1e-6 * np.eye(n_features)

    return weights, means, covariances

def compute_log_likelihood(X, weights, means, covariances):
    """Compute log-likelihood of the data"""
    log_likelihood = 0
    n_components = len(weights)

    for i in range(X.shape[0]):
        sample_likelihood = 0
        for k in range(n_components):
            sample_likelihood += weights[k] * gaussian_pdf(X[i:i+1], means[k], covariances[k])[0]
        log_likelihood += np.log(sample_likelihood + 1e-10)

    return log_likelihood

def fit_gmm_em(X, n_components=2, max_iter=100, tol=1e-6):
    """Fit GMM using EM algorithm"""
    # Initialize parameters
    weights, means, covariances = initialize_gmm_parameters(X, n_components)
    log_likelihoods = []

    for iteration in range(max_iter):
        # E-step
        responsibilities = e_step(X, weights, means, covariances)

        # M-step
        weights, means, covariances = m_step(X, responsibilities)

        # Compute log-likelihood
        log_likelihood = compute_log_likelihood(X, weights, means, covariances)
        log_likelihoods.append(log_likelihood)

        # Check for convergence
        if iteration > 0 and abs(log_likelihoods[-1] - log_likelihoods[-2]) < tol:
            print(f"EM algorithm converged after {iteration + 1} iterations")
            break

    return weights, means, covariances, log_likelihoods, responsibilities

def predict_gmm(X, weights, means, covariances):
    """Predict cluster assignments using fitted GMM"""
    responsibilities = e_step(X, weights, means, covariances)
    return np.argmax(responsibilities, axis=1), responsibilities

def load_data():
    """Load MNIST data for digits 2 and 6"""

    images = np.loadtxt('data/data.dat')
    true_labels = np.loadtxt('data/label.dat', dtype=int)

    return images, true_labels

def apply_pca(X, n_components=4):
    """Apply PCA to reduce dimensionality"""
    pca = PCA(n_components=n_components)
    X_reduced = pca.fit_transform(X)
    return X_reduced, pca

def plot_log_likelihood_convergence(log_likelihoods):
    """Plot log-likelihood convergence"""
    plt.figure(figsize=(10, 6))
    plt.plot(log_likelihoods, 'b-', linewidth=2, marker='o')
    plt.title('Log-Likelihood vs Iterations (EM Algorithm Convergence)', fontsize=14)
    plt.xlabel('Iteration', fontsize=12)
    plt.ylabel('Log-Likelihood', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

def visualize_component_means(means, weights, pca):
    """Visualize component means as images"""
    _, axes = plt.subplots(1, 2, figsize=(12, 5))

    for k in range(2):
        # Transform back to original space
        mean_original = pca.inverse_transform(means[k].reshape(1, -1))
        mean_image = mean_original.reshape(28, 28)

        im = axes[k].imshow(mean_image, cmap='gray')
        axes[k].set_title(f'Component {k+1} Mean\n(Weight: {weights[k]:.3f})', fontsize=12)
        axes[k].axis('off')
        plt.colorbar(im, ax=axes[k])

    plt.tight_layout()
    plt.show()

def visualize_covariance_matrices(covariances, weights):
    """Visualize covariance matrices as heatmaps"""
    _, axes = plt.subplots(1, 2, figsize=(14, 6))

    for k in range(2):
        im = axes[k].imshow(covariances[k], cmap='viridis')
        axes[k].set_title(f'Component {k+1} Covariance Matrix\n(Weight: {weights[k]:.3f})', fontsize=12)
        plt.colorbar(im, ax=axes[k])

        # Add text annotations
        for i in range(4):
            for j in range(4):
                axes[k].text(j, i, f'{covariances[k][i, j]:.3f}',
                           ha="center", va="center", color="white", fontsize=10)

    plt.tight_layout()
    plt.show()

def visualize_sample_predictions(X_pca, pca, true_labels, predicted_labels, n_samples=6):
    """Visualize sample images with predictions"""
    sample_indices = np.random.choice(len(predicted_labels), n_samples, replace=False)

    _, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()

    for i, idx in enumerate(sample_indices):
        # Get original image
        original_image = pca.inverse_transform(X_pca[idx].reshape(1, -1))
        image = original_image.reshape(28, 28)

        axes[i].imshow(image, cmap='gray')
        axes[i].set_title(f'True: {true_labels[idx]}, Pred: {predicted_labels[idx]}', fontsize=12)
        axes[i].axis('off')

    plt.tight_layout()
    plt.show()

def calculate_misclassification_rate(true_labels, predicted_labels):
    """Calculate misclassification rates for each digit"""
    # Map predicted labels to true labels (handle label switching)
    # Find the best mapping between predicted and true labels
    unique_true = np.unique(true_labels)
    unique_pred = np.unique(predicted_labels)
    
    # Try both possible mappings
    mapping1 = {unique_pred[0]: unique_true[0], unique_pred[1]: unique_true[1]}
    mapping2 = {unique_pred[0]: unique_true[1], unique_pred[1]: unique_true[0]}
    
    mapped_pred1 = np.array([mapping1[p] for p in predicted_labels])
    mapped_pred2 = np.array([mapping2[p] for p in predicted_labels])
    
    accuracy1 = np.mean(mapped_pred1 == true_labels)
    accuracy2 = np.mean(mapped_pred2 == true_labels)
    
    # Choose the mapping with higher accuracy
    if accuracy1 >= accuracy2:
        mapped_pred = mapped_pred1
        accuracy = accuracy1
    else:
        mapped_pred = mapped_pred2
        accuracy = accuracy2
    
    # Calculate misclassification rates for each digit
    digit_2_mask = true_labels == 2
    digit_6_mask = true_labels == 6
    
    digit_2_misclassification = 1 - np.mean(mapped_pred[digit_2_mask] == true_labels[digit_2_mask])
    digit_6_misclassification = 1 - np.mean(mapped_pred[digit_6_mask] == true_labels[digit_6_mask])
    
    overall_misclassification = 1 - accuracy
    
    return digit_2_misclassification, digit_6_misclassification, overall_misclassification

# Main execution
if __name__ == "__main__":
    print("Loading MNIST data (digits 2 and 6)...")
    images, true_labels = load_data()

    if images is None:
        print("Failed to load data. Please check file paths.")
        exit()

    print(f"Data shape: {images.shape}")
    print(f"Labels shape: {true_labels.shape}")
    print(f"Unique labels: {np.unique(true_labels)}")

    # Apply PCA to reduce dimensionality to 4
    print("\nApplying PCA to reduce dimensionality to 4...")
    X_pca, pca = apply_pca(images, n_components=4)
    print(f"PCA transformed data shape: {X_pca.shape}")
    print(f"Explained variance ratio: {pca.explained_variance_ratio_}")
    print(f"Total explained variance: {np.sum(pca.explained_variance_ratio_):.4f}")

    # =============================================================================
    print("\n" + "="*50)
    print("3.1 (10 points) Implement EM algorithm yourself. Use the following initialization")
    print("="*50)

    # Set random seed
    np.random.seed(6740)

    # Fit GMM using EM algorithm
    print("Fitting Gaussian Mixture Model using EM algorithm...")
    print("Initialization:")
    print("- Mean: random Gaussian vector with zero mean")

    weights, means, covariances, log_likelihoods, responsibilities = fit_gmm_em(
        X_pca, n_components=2, max_iter=100, tol=1e-6
    )

    print(f"Final log-likelihood: {log_likelihoods[-1]:.4f}")
    print("Plotting log-likelihood function versus number of iterations...")
    plot_log_likelihood_convergence(log_likelihoods)

    # =============================================================================
    print("\n" + "="*50)
    print("3.2 (20 points) Report the fitted GMM model when EM has terminated in your algorithms")
    print("as follows:")
    print("="*50)

    # Get predictions
    predicted_labels, final_responsibilities = predict_gmm(X_pca, weights, means, covariances)

    print("\nNumerical weights for each component:")
    for k in range(2):
        print(f"  Component {k+1}: {weights[k]:.6f}")

    print(f"\nMean of each component (in PCA space):")
    for k in range(2):
        print(f"  Component {k+1}: {means[k]}")

    print("\nDisplaying component means as 28x28 images (mapped back to original space)...")
    visualize_component_means(means, weights, pca)

    print("\nTwo 4x4 covariance matrices:")
    for k in range(2):
        print(f"\nComponent {k+1} Covariance Matrix:")
        print(covariances[k])

    print("\nVisualizing covariance matrices as heatmaps...")
    visualize_covariance_matrices(covariances, weights)

    # =============================================================================
    print("\n" + "="*50)
    print("3.3 (10 points) Use the tau_k^i to infer the labels of the images, and compare with the true labels.")
    print("Report the mis-classification rate (1 - Accuracy) for digits \"2\" and \"6\" respectively.")
    print("="*50)

    # Calculate misclassification rates for GMM
    digit_2_miscl_gmm, digit_6_miscl_gmm, overall_miscl_gmm = calculate_misclassification_rate(
        true_labels, predicted_labels
    )

    print("\nGMM Results using tau_k^i (responsibilities):")
    print(f"Misclassification rates (GMM):")
    print(f"  Digit 2: {digit_2_miscl_gmm:.4f} ({digit_2_miscl_gmm*100:.2f}%)")
    print(f"  Digit 6: {digit_6_miscl_gmm:.4f} ({digit_6_miscl_gmm*100:.2f}%)")
    print(f"  Overall: {overall_miscl_gmm:.4f} ({overall_miscl_gmm*100:.2f}%)")

    # Compare with K-means
    print("\nComparing with K-means clustering (K=2)...")
    kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
    kmeans_labels = kmeans.fit_predict(X_pca)

    # Calculate misclassification rates for K-means
    digit_2_miscl_kmeans, digit_6_miscl_kmeans, overall_miscl_kmeans = calculate_misclassification_rate(
        true_labels, kmeans_labels
    )

    print(f"\nK-means Results:")
    print(f"Misclassification rates (K-means):")
    print(f"  Digit 2: {digit_2_miscl_kmeans:.4f} ({digit_2_miscl_kmeans*100:.2f}%)")
    print(f"  Digit 6: {digit_6_miscl_kmeans:.4f} ({digit_6_miscl_kmeans*100:.2f}%)")
    print(f"  Overall: {overall_miscl_kmeans:.4f} ({overall_miscl_kmeans*100:.2f}%)")

    print(f"\nComparison:")
    if overall_miscl_gmm < overall_miscl_kmeans:
        print("GMM achieves better performance than K-means")
        print(f"GMM is better by {(overall_miscl_kmeans - overall_miscl_gmm)*100:.2f} percentage points")
    elif overall_miscl_gmm > overall_miscl_kmeans:
        print("K-means achieves better performance than GMM")
        print(f"K-means is better by {(overall_miscl_gmm - overall_miscl_kmeans)*100:.2f} percentage points")
    else:
        print("GMM and K-means achieve similar performance")

    # Additional visualizations
    print("\nGenerating sample prediction visualizations...")
    visualize_sample_predictions(X_pca, pca, true_labels, predicted_labels)
